{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:05:54,219+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:06:15,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:06:15,821+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:07:41,996+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:07:42,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:08:14,092+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:09:30,966+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:09:32,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:10:11,071+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:10:11,943+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:15:43,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:15:44,283+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:15:45,285+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:15:46,330+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:18:13,085+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:18:14,003+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:19:26,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:19:30,513+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:29:52,603+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:29:56,797+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:59:52,073+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:59:53,187+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 19:01:32,951+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 19:01:34,093+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 19:02:28,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 19:02:29,543+0530","service":"FyersAPIRequest"}
