# Reinforcement Learning Model Architecture

## 1. Executive Summary

This document outlines the architectural vision for the Reinforcement Learning (RL) component of the Autonomous Trading Bot. The core principle is to develop an **instrument-agnostic** RL model capable of generating universal trading signals (`BUY`, `SELL`, `HOLD`, `CLOSE`) across diverse market data (stocks, indices, various timeframes) in the Indian market. The architecture uses **PPO (Proximal Policy Optimization)** for robust trading strategy learning.

## 2. Core Architectural Principles

*   **Instrument Agnosticism:** The RL model's input features will be normalized and standardized to allow it to learn patterns independent of specific instrument types (stocks, futures, options) or timeframes. The model will not inherently distinguish between these.
*   **Simplified Action Space:** The model's output will be limited to four fundamental trading actions: `BUY`, `SELL`, `HOLD`, `CLOSE`. The decision of *which* specific instrument to apply these actions to will be handled by an external, higher-level logic.
*   **PPO-Based Learning:** The system uses Proximal Policy Optimization (PPO) for stable and efficient reinforcement learning, providing robust performance across different market conditions.
*   **Realistic Simulation:** The backtesting environment will accurately simulate Indian market conditions, including fixed brokerage costs (25 INR entry, 35 INR exit) and negligible slippage.

## 3. Architectural Components

### 3.1. Data & Feature Layer

*   **Purpose:** Provide clean, normalized, and relevant market data to the RL environment.
*   **Components:**
    *   **`data/final/`:** Repository for pre-processed, feature-engineered historical data (OHLCV, technical indicators). This data will be diverse, covering various instruments and timeframes.
    *   **`src/utils/data_loader.py`:** Responsible for efficiently loading data from `data/final/` and preparing it for the backtesting environment.
    *   **Feature Normalization/Standardization:** All features fed to the RL model will undergo **Z-score normalization (StandardScaler)** to ensure instrument agnosticism and optimal learning.

### 3.2. Backtesting Environment (`src/backtesting/environment.py`)

*   **Purpose:** The "world" in which the RL agents learn and are evaluated. It simulates market interactions and provides feedback.
*   **Key Responsibilities:**
    *   **State Representation (Observation Space):**
        *   Includes **all features generated by `feature_generator.py`**.
        *   Incorporates a **lookback window of 50 periods** of historical data.
        *   Explicitly includes the **current account state**: normalized current capital, current position (e.g., 0 for no position, 1 for long, -1 for short), and normalized unrealized P&L.
        *   All components of the observation space will be normalized using Z-score scaling.
    *   **Action Execution:** Translates the RL agent's discrete actions (`BUY`, `SELL`, `HOLD`, `CLOSE`) into simulated trades within the `BacktestingEngine`.
    *   **Reward Calculation:** Provides a reward signal to the agent based on a composite function designed for high profitability:
        *   **Primary:** **Change in portfolio value (P&L) per step, after accounting for transaction costs.**
        *   **Secondary Penalties/Bonuses:**
            *   Small penalty for holding a losing position (encourages timely exits).
            *   Small bonus for closing a profitable position (reinforces good exits).
            *   Penalty for invalid actions (e.g., trying to sell when no position, or buying with insufficient capital).
            *   Small penalty if the portfolio value drops significantly or if the maximum drawdown increases (encourages risk management).
    *   **Episode Management:** Handles the start and end of trading simulations (episodes).
    *   **Transaction Cost Simulation:** Incorporates the fixed brokerage costs (25 INR entry, 35 INR exit) and negligible slippage.

### 3.3. Backtesting Engine (`src/backtesting/engine.py`)

*   **Purpose:** The core simulation logic that underpins the `TradingEnv`.
*   **Key Responsibilities:**
    *   Manages the simulated trading account (capital, positions).
    *   Executes simulated trades based on prices from the historical data.
    *   Applies transaction costs.
    *   Tracks P&L and trade history.

### 3.4. Reinforcement Learning Agents (`src/agents/`)

*   **Purpose:** The decision-making entities that learn optimal trading policies.
*   **Components:**
    *   **`src/agents/base_agent.py`:** Defines a common interface for all RL agents, ensuring consistency.
    *   **`src/agents/ppo_agent.py`:** The main PPO-based RL agent that learns optimal trading strategies across all market conditions. This agent is trained on the full, diverse dataset and outputs `BUY`/`SELL`/`HOLD`/`CLOSE` actions.

### 3.5. Neural Network Models (`src/models/`)

*   **Purpose:** Provide the underlying neural network architectures for the RL agents.
*   **Components:**
    *   **`src/models/lstm_model.py`:** Generic LSTM/GRU-based network for processing sequential market data and learning state representations. This will be adaptable for both baseline and specialized agents.
    *   Other potential models (e.g., CNN, Transformer) as needed for specialized agents.

### 3.6. Training & Evaluation (`src/training/`, `src/utils/metrics.py`)

*   **Purpose:** Orchestrate the learning process and assess agent performance.
*   **Components:**
    *   **`src/training/trainer.py`:** Manages the PPO training loops, interacting with the `TradingEnv`, updating agent policies, and logging progress.
    *   **`src/utils/metrics.py`:** Implements standard trading performance metrics for rigorous evaluation.

## 4. Interaction Flow (High-Level)

1.  **Data Preparation:** Raw data is processed into features and stored in `data/final/`.
2.  **Environment Initialization:** `TradingEnv` loads data via `DataLoader` and initializes `BacktestingEngine`.
3.  **Training Loop (MAML-driven):**
    *   **Task Sampling:** For each meta-training iteration, a batch of diverse tasks (instrument/timeframe combinations) is sampled from the `data/final` dataset.
    *   **Inner Loop (Adaptation):** For each sampled task, the RL agent performs a few gradient steps to adapt its policy to that specific task.
    *   **Outer Loop (Meta-Update):** The meta-learner (MAML) updates the agent's initial parameters based on the performance across all adapted tasks, aiming for a good initialization that allows for rapid adaptation to *new* unseen tasks.
    *   `TradingEnv` provides an observation (market state) to the RL agent.
    *   RL agent (e.g., PPO or MoE) processes the observation through its neural network model and selects an action (`BUY`, `SELL`, `HOLD`, `CLOSE`).
    *   `TradingEnv` passes the action to the `BacktestingEngine`.
    *   `BacktestingEngine` simulates the trade, updates account, and calculates instantaneous P&L.
    *   `TradingEnv` returns the next observation, reward, and termination status to the RL agent.
    *   RL agent uses the reward to update its policy (learn).
4.  **Evaluation:** Trained agents are run through the `TradingEnv` (without policy updates) and their performance is assessed using `src/utils/metrics.py`.

## 5. Evaluation Metrics & Success Criteria

To ensure high profitability and robust performance, the following metrics will be tracked and targeted during backtesting:

*   **Primary Metrics (Profitability & Risk-Adjusted Return):**
    *   **Sharpe Ratio:** Target > 1.5 (indicating strong risk-adjusted returns).
    *   **Total P&L:** Maximize (absolute profit generated).
    *   **Profit Factor:** Target > 1.2 (ratio of gross profit to gross loss, indicating profitable strategy).
*   **Secondary Metrics (Risk Management & Efficiency):**
    *   **Maximum Drawdown:** Target < 15% (to control capital risk).
    *   **Win Rate:** Target > 50% (percentage of profitable trades).
    *   **Average P&L per Trade:** Maximize.
    *   **Number of Trades:** Monitor to ensure efficient trading and avoid excessive transaction costs.
